import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export interface JWTPayload {
  userId: string
  email: string
  name: string
  role: UserRole
  iat?: number
  exp?: number
}

export interface AuthUser {
  id: string
  email: string
  name: string
  role: UserRole
  branches?: Array<{
    id: string
    name: string
    address: string
  }>
}

/**
 * Generate JWT access token
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  const jwtSecret = process.env.NEXTAUTH_SECRET
  if (!jwtSecret) {
    throw new Error('JWT secret not configured')
  }

  return jwt.sign(payload, jwtSecret, {
    expiresIn: '1h',
    issuer: 'shoptool',
    audience: 'shoptool-users'
  })
}

/**
 * Generate JWT refresh token
 */
export function generateRefreshToken(userId: string): string {
  const jwtSecret = process.env.NEXTAUTH_SECRET
  if (!jwtSecret) {
    throw new Error('JWT secret not configured')
  }

  return jwt.sign(
    { userId },
    jwtSecret,
    {
      expiresIn: '7d',
      issuer: 'shoptool',
      audience: 'shoptool-users'
    }
  )
}

/**
 * Verify and decode JWT token
 */
export function verifyToken(token: string): JWTPayload {
  const jwtSecret = process.env.NEXTAUTH_SECRET
  if (!jwtSecret) {
    throw new Error('JWT secret not configured')
  }

  try {
    const decoded = jwt.verify(token, jwtSecret, {
      issuer: 'shoptool',
      audience: 'shoptool-users'
    }) as JWTPayload
    
    return decoded
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired')
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token')
    }
    throw new Error('Token verification failed')
  }
}

/**
 * Extract token from request headers or cookies
 */
export function extractTokenFromRequest(request: NextRequest): string | null {
  // Try to get token from Authorization header first
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Fallback to cookie
  const tokenFromCookie = request.cookies.get('accessToken')?.value
  return tokenFromCookie || null
}

/**
 * Get authenticated user from request
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<AuthUser | null> {
  try {
    const token = extractTokenFromRequest(request)
    if (!token) {
      return null
    }

    const payload = verifyToken(token)
    
    // Fetch fresh user data from database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        branches: {
          include: {
            branch: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      branches: user.branches.map(ub => ub.branch)
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

/**
 * Check if user has required role
 */
export function hasRole(user: AuthUser, requiredRole: UserRole): boolean {
  if (requiredRole === UserRole.STAFF) {
    // Both OWNER and STAFF can access STAFF-level resources
    return user.role === UserRole.OWNER || user.role === UserRole.STAFF
  }
  
  if (requiredRole === UserRole.OWNER) {
    // Only OWNER can access OWNER-level resources
    return user.role === UserRole.OWNER
  }
  
  return false
}

/**
 * Check if user has access to specific branch
 */
export function hasAccessToBranch(user: AuthUser, branchId: string): boolean {
  // Owners have access to all branches
  if (user.role === UserRole.OWNER) {
    return true
  }
  
  // Staff members only have access to their assigned branches
  return user.branches?.some(branch => branch.id === branchId) || false
}