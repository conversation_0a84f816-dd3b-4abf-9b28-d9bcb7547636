import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { 
  hashPassword, 
  passwordSchema, 
  emailSchema, 
  nameSchema,
  registerRateLimiter,
  getClientIP,
  applySecurityHeaders
} from '@/lib/security'

// Validation schema for registration
const registerSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
  role: z.enum(['OWNER', 'STAFF']).optional().default('STAFF'),
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = getClientIP(request)
    if (!registerRateLimiter.isAllowed(clientIP)) {
      const timeUntilReset = registerRateLimiter.getTimeUntilReset(clientIP)
      return applySecurityHeaders(NextResponse.json(
        { 
          error: 'Too many registration attempts. Please try again later.',
          retryAfter: timeUntilReset
        },
        { status: 429 }
      ))
    }
    const body = await request.json()
    
    // Validate request data
    const validatedData = registerSchema.parse(body)
    const { name, email, password, role } = validatedData

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: role as UserRole,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      }
    })

    return applySecurityHeaders(NextResponse.json(
      {
        success: true,
        message: 'User registered successfully',
        data: user
      },
      { status: 201 }
    ))

  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return applySecurityHeaders(NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      ))
    }

    return applySecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ))
  }
}