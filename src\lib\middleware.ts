import { NextRequest, NextResponse } from 'next/server'
import { getAuthenticatedUser, hasRole, hasAccessToBranch, AuthUser } from '@/lib/auth'
import { UserRole } from '@prisma/client'

export interface AuthenticatedRequest extends NextRequest {
  user?: AuthUser
}

/**
 * Authentication middleware - verifies JWT token and attaches user to request
 */
export async function with<PERSON>uth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const user = await getAuthenticatedUser(request)
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Attach user to request
    const authenticatedRequest = request as AuthenticatedRequest
    authenticatedRequest.user = user

    return await handler(authenticatedRequest)
  } catch (error) {
    console.error('Authentication middleware error:', error)
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 401 }
    )
  }
}

/**
 * Role-based access control middleware
 */
export async function withRole(
  requiredRole: UserRole,
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return withAuth(request, async (req) => {
    const user = req.user!
    
    if (!hasRole(user, requiredRole)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    return await handler(req)
  })
}

/**
 * Branch access control middleware
 */
export async function withBranchAccess(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>,
  getBranchId?: (req: NextRequest) => string | null
): Promise<NextResponse> {
  return withAuth(request, async (req) => {
    const user = req.user!
    
    // If no getBranchId function provided, skip branch check
    if (!getBranchId) {
      return await handler(req)
    }

    const branchId = getBranchId(req)
    if (!branchId) {
      return NextResponse.json(
        { error: 'Branch ID required' },
        { status: 400 }
      )
    }

    if (!hasAccessToBranch(user, branchId)) {
      return NextResponse.json(
        { error: 'Access denied to this branch' },
        { status: 403 }
      )
    }

    return await handler(req)
  })
}

/**
 * Owner-only middleware
 */
export async function withOwnerAccess(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return withRole(UserRole.OWNER, request, handler)
}

/**
 * Staff or Owner middleware (any authenticated user)
 */
export async function withStaffAccess(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return withRole(UserRole.STAFF, request, handler)
}

/**
 * Utility to extract branch ID from URL parameters
 */
export function getBranchIdFromParams(request: NextRequest): string | null {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  
  // Look for branch ID in common patterns like /api/branches/{id} or /api/{branchId}/...
  const branchIndex = pathSegments.findIndex(segment => segment === 'branches')
  if (branchIndex !== -1 && pathSegments[branchIndex + 1]) {
    return pathSegments[branchIndex + 1]
  }
  
  return null
}

/**
 * Utility to extract branch ID from query parameters
 */
export function getBranchIdFromQuery(request: NextRequest): string | null {
  const url = new URL(request.url)
  return url.searchParams.get('branchId')
}

/**
 * Utility to extract branch ID from request body
 */
export async function getBranchIdFromBody(request: NextRequest): Promise<string | null> {
  try {
    const body = await request.json()
    return body.branchId || null
  } catch {
    return null
  }
}