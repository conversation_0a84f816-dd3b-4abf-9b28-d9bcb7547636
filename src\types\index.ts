// User types
export interface User {
  id: string
  email: string
  name: string
  role: 'USER' | 'ADMIN'
  createdAt: Date
  updatedAt: Date
}

// Product types
export interface Product {
  id: string
  name: string
  description: string
  price: number
  imageUrl?: string
  category: string
  stock: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Order types
export interface Order {
  id: string
  userId: string
  items: OrderItem[]
  total: number
  status: 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  shippingAddress: Address
  createdAt: Date
  updatedAt: Date
}

export interface OrderItem {
  id: string
  productId: string
  quantity: number
  price: number
  product?: Product
}

// Address types
export interface Address {
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Auth types
export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  name: string
}

export interface AuthUser {
  id: string
  email: string
  name: string
  role: string
}