// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and role management
model User {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  password  String // hashed password
  role      UserRole @default(STAFF)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Many-to-many relationship with branches
  branches UserBranch[]

  // Relations to invoices as cashier
  invoices Invoice[]

  @@index([email])
  @@map("users")
}

// Branch model for shop locations
model Branch {
  id            String   @id @default(cuid())
  name          String
  address       String
  contactNumber String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Many-to-many relationship with users
  users UserBranch[]

  // Relations to other entities
  products Product[]
  invoices Invoice[]

  @@index([name])
  @@map("branches")
}

// Junction table for User-Branch many-to-many relationship
model UserBranch {
  id       String @id @default(cuid())
  userId   String
  branchId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  branch Branch @relation(fields: [branchId], references: [id], onDelete: Cascade)

  @@unique([userId, branchId])
  @@map("user_branches")
}

// Product model for inventory management
model Product {
  id           String   @id @default(cuid())
  name         String
  description  String?
  sku          String   @unique
  barcode      String?  @unique
  basePrice    Decimal  @db.Decimal(10, 2)
  sellingPrice Decimal  @db.Decimal(10, 2)
  category     String?
  imageUrl     String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Inventory tracking fields with validation
  currentStock Int @default(0) // Current stock level
  minimumStock Int @default(0) // Minimum stock threshold
  reorderPoint Int @default(0) // Reorder point threshold

  // Relations
  branchId String
  branch   Branch @relation(fields: [branchId], references: [id], onDelete: Cascade)

  // Relations to invoice items
  invoiceItems InvoiceItem[]

  @@index([name])
  @@index([sku])
  @@index([barcode])
  @@index([branchId])
  @@map("products")
}

// Invoice model for billing
model Invoice {
  id             String         @id @default(cuid())
  invoiceNumber  String         @unique
  date           DateTime       @default(now())
  subtotal       Decimal        @db.Decimal(10, 2)
  taxAmount      Decimal        @default(0) @db.Decimal(10, 2)
  discountAmount Decimal        @default(0) @db.Decimal(10, 2)
  totalAmount    Decimal        @db.Decimal(10, 2)
  paymentStatus  PaymentStatus  @default(PENDING)
  paymentMethod  PaymentMethod?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  cashierId String
  cashier   User   @relation(fields: [cashierId], references: [id])

  branchId String
  branch   Branch @relation(fields: [branchId], references: [id])

  // One-to-many relationship with invoice items
  items InvoiceItem[]

  @@index([invoiceNumber])
  @@index([date])
  @@index([cashierId])
  @@index([branchId])
  @@map("invoices")
}

// InvoiceItem model for line items in invoices
model InvoiceItem {
  id        String   @id @default(cuid())
  quantity  Int      @default(1) // Ensure minimum quantity
  unitPrice Decimal  @db.Decimal(10, 2)
  subtotal  Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now())

  // Relations
  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Restrict)

  @@unique([invoiceId, productId]) // Prevent duplicate products in same invoice
  @@index([invoiceId])
  @@index([productId])
  @@map("invoice_items")
}

// Enum for user roles
enum UserRole {
  OWNER
  STAFF
}

// Enum for payment status
enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  CANCELLED
  REFUNDED
}

// Enum for payment methods
enum PaymentMethod {
  CASH
  CARD
  DIGITAL_WALLET
  BANK_TRANSFER
  CHECK
}
