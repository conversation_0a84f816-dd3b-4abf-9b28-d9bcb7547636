import Layout from '@/components/Layout'
import { Button } from '@/components/ui'
import Link from 'next/link'

export default function Home() {
  return (
    <Layout>
      <div className="hero min-h-[60vh] bg-base-200 rounded-lg">
        <div className="hero-content text-center">
          <div className="max-w-lg">
            <h1 className="text-5xl font-bold text-primary">MobileBill Pro</h1>
            <p className="py-6 text-lg">
              Beautiful, simple, and intuitive billing software with inventory management for mobile shops. 
              Manage multiple branches with secure authentication for owners and staff.
            </p>
            <div className="flex gap-4 justify-center">
              <Link href="/login">
                <Button variant="primary" size="lg">
                  Get Started
                </Button>
              </Link>
              <Link href="/dashboard">
                <Button variant="outline" size="lg">
                  View Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-4xl mb-4">📱</div>
            <h2 className="card-title">Billing & Invoicing</h2>
            <p>Generate invoices, track payments, and handle returns for mobile phones and accessories.</p>
            <div className="card-actions justify-end">
              <Link href="/billing">
                <Button variant="primary">Create Invoice</Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-4xl mb-4">📦</div>
            <h2 className="card-title">Inventory Management</h2>
            <p>Track stock levels, manage suppliers, and handle transfers between branches.</p>
            <div className="card-actions justify-end">
              <Link href="/inventory">
                <Button variant="primary">Manage Stock</Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-4xl mb-4">🏪</div>
            <h2 className="card-title">Multi-Branch Support</h2>
            <p>Manage multiple shop locations from a single dashboard with role-based access.</p>
            <div className="card-actions justify-end">
              <Link href="/branches">
                <Button variant="primary">View Branches</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-4xl mb-4">📊</div>
            <h2 className="card-title">Reports & Analytics</h2>
            <p>Generate sales reports, inventory reports, and get insights into business performance.</p>
            <div className="card-actions justify-end">
              <Link href="/reports">
                <Button variant="primary">View Reports</Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-4xl mb-4">👥</div>
            <h2 className="card-title">User Management</h2>
            <p>Secure authentication with role-based access for owners and staff members.</p>
            <div className="card-actions justify-end">
              <Link href="/users">
                <Button variant="primary">Manage Users</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
