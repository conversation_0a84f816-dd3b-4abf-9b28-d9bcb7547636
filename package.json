{"name": "temp-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.12.0", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-next": "^0.6.2", "@types/bcrypt": "^6.0.0", "@types/jsonwebtoken": "^9.0.10", "bcrypt": "^6.0.0", "daisyui": "^5.0.46", "jsonwebtoken": "^9.0.2", "next": "15.4.4", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}