import React from 'react'
import { cn } from '@/utils'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  variant?: 'bordered' | 'ghost' | 'primary' | 'secondary' | 'accent'
  inputSize?: 'xs' | 'sm' | 'md' | 'lg'
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  variant = 'bordered',
  inputSize = 'md',
  className,
  ...props
}) => {
  const baseClasses = 'input w-full'
  
  const variantClasses = {
    bordered: 'input-bordered',
    ghost: 'input-ghost',
    primary: 'input-primary',
    secondary: 'input-secondary',
    accent: 'input-accent',
  }
  
  const sizeClasses = {
    xs: 'input-xs',
    sm: 'input-sm',
    md: '',
    lg: 'input-lg',
  }

  return (
    <div className="form-control w-full">
      {label && (
        <label className="label">
          <span className="label-text">{label}</span>
        </label>
      )}
      
      <input
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[inputSize],
          error && 'input-error',
          className
        )}
        {...props}
      />
      
      {(error || helperText) && (
        <label className="label">
          <span className={cn(
            'label-text-alt',
            error ? 'text-error' : 'text-base-content/70'
          )}>
            {error || helperText}
          </span>
        </label>
      )}
    </div>
  )
}

export default Input