# Database Setup Guide

## Overview
This project uses PostgreSQL as the database with Prisma as the ORM. The database schema includes models for Users, Branches, Products, Invoices, and InvoiceItems.

## Prerequisites
- PostgreSQL server running locally or remotely
- Node.js and npm installed
- Environment variables configured

## Environment Setup

1. **Configure Database URL**
   Update the `DATABASE_URL` in your `.env` file:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/shoptool_db?schema=public"
   ```
   Replace `username`, `password`, and database details with your actual PostgreSQL credentials.

2. **Install Dependencies**
   ```bash
   npm install
   ```

## Database Migration

### Initial Migration
To create the database schema for the first time:

```bash
# Generate and apply the initial migration
npx prisma migrate dev --name init
```

This command will:
- Create the database if it doesn't exist
- Generate migration files in `prisma/migrations/`
- Apply the migration to create all tables, indexes, and constraints
- Generate the Prisma client

### Subsequent Migrations
When you make changes to the schema:

```bash
# Generate a new migration
npx prisma migrate dev --name describe_your_changes
```

### Production Migrations
For production environments:

```bash
# Deploy migrations without prompts
npx prisma migrate deploy
```

## Database Seeding

To populate the database with initial test data:

```bash
# Run the seed script
npx prisma db seed
```

Add this to your `package.json` to enable the seed command:
```json
{
  "prisma": {
    "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"
  }
}
```

## Database Schema Overview

### Models

1. **User** - Authentication and role management
   - Fields: id, name, email, password, role, timestamps
   - Relations: Many-to-many with Branches, One-to-many with Invoices

2. **Branch** - Shop locations
   - Fields: id, name, address, contactNumber, timestamps
   - Relations: Many-to-many with Users, One-to-many with Products and Invoices

3. **Product** - Inventory items
   - Fields: id, name, description, SKU, barcode, prices, stock levels, timestamps
   - Relations: Belongs to Branch, One-to-many with InvoiceItems

4. **Invoice** - Billing records
   - Fields: id, invoiceNumber, amounts, payment info, timestamps
   - Relations: Belongs to User (cashier) and Branch, One-to-many with InvoiceItems

5. **InvoiceItem** - Line items in invoices
   - Fields: id, quantity, prices, timestamps
   - Relations: Belongs to Invoice and Product

### Key Features
- **Referential Integrity**: Proper foreign key constraints
- **Cascade Deletes**: Configured for dependent records
- **Indexes**: Optimized for common queries
- **Unique Constraints**: Prevent duplicate data
- **Default Values**: Sensible defaults for optional fields

## Useful Prisma Commands

```bash
# View database in Prisma Studio
npx prisma studio

# Reset database (WARNING: Deletes all data)
npx prisma migrate reset

# Generate Prisma client after schema changes
npx prisma generate

# Check migration status
npx prisma migrate status

# Format schema file
npx prisma format
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database user has proper permissions

2. **Migration Conflicts**
   - Use `npx prisma migrate reset` to start fresh (development only)
   - Resolve conflicts manually in production

3. **Schema Validation Errors**
   - Run `npx prisma validate` to check schema syntax
   - Use `npx prisma format` to fix formatting

### Development vs Production

- **Development**: Use `prisma migrate dev` for interactive migrations
- **Production**: Use `prisma migrate deploy` for automated deployments
- **Always backup production data before migrations**

## Security Considerations

- Store database credentials securely
- Use environment-specific `.env` files
- Hash passwords before storing (use bcrypt)
- Implement proper access controls
- Regular database backups