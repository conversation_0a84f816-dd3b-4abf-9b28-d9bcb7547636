import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { generateAccessToken, generateRefreshToken } from '@/lib/auth'
import { 
  verifyPassword, 
  emailSchema,
  loginRateLimiter,
  getClientIP,
  applySecurityHeaders
} from '@/lib/security'

// Validation schema for login
const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = getClientIP(request)
    if (!loginRateLimiter.isAllowed(clientIP)) {
      const timeUntilReset = loginRateLimiter.getTimeUntilReset(clientIP)
      return applySecurityHeaders(NextResponse.json(
        { 
          error: 'Too many login attempts. Please try again later.',
          retryAfter: timeUntilReset
        },
        { status: 429 }
      ))
    }
    const body = await request.json()
    
    // Validate request data
    const validatedData = loginSchema.parse(body)
    const { email, password } = validatedData

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        branches: {
          include: {
            branch: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password)
    
    if (!isPasswordValid) {
      return applySecurityHeaders(NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      ))
    }

    // Generate JWT tokens
    const accessToken = generateAccessToken({
      userId: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    })

    const refreshToken = generateRefreshToken(user.id)

    // Prepare user data for response (exclude password)
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      branches: user.branches.map(ub => ub.branch),
      createdAt: user.createdAt,
    }

    // Create response with HTTP-only cookies
    const response = applySecurityHeaders(NextResponse.json(
      {
        success: true,
        message: 'Login successful',
        data: {
          user: userData,
          accessToken
        }
      },
      { status: 200 }
    ))

    // Set HTTP-only cookies for tokens
    response.cookies.set('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60, // 1 hour
      path: '/'
    })

    response.cookies.set('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/'
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    
    if (error instanceof z.ZodError) {
      return applySecurityHeaders(NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      ))
    }
    
    return applySecurityHeaders(NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    ))
  }
}