import { PrismaClient, UserRole, PaymentStatus, PaymentMethod } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create branches
  const mainBranch = await prisma.branch.create({
    data: {
      name: 'Main Store',
      address: '123 Main Street, City, State 12345',
      contactNumber: '+1-555-0123',
    },
  })

  const secondBranch = await prisma.branch.create({
    data: {
      name: 'Downtown Branch',
      address: '456 Downtown Ave, City, State 12345',
      contactNumber: '+1-555-0456',
    },
  })

  console.log('✅ Created branches')

  // Create users
  const owner = await prisma.user.create({
    data: {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '$2b$10$hashedpassword', // In real app, hash with bcrypt
      role: UserRole.OWNER,
    },
  })

  const staff = await prisma.user.create({
    data: {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '$2b$10$hashedpassword', // In real app, hash with bcrypt
      role: UserRole.STAFF,
    },
  })

  console.log('✅ Created users')

  // Assign users to branches
  await prisma.userBranch.createMany({
    data: [
      { userId: owner.id, branchId: mainBranch.id },
      { userId: owner.id, branchId: secondBranch.id },
      { userId: staff.id, branchId: mainBranch.id },
    ],
  })

  console.log('✅ Assigned users to branches')

  // Create products individually to get their IDs
  const laptopProduct = await prisma.product.create({
    data: {
      name: 'Laptop Computer',
      description: 'High-performance laptop for business use',
      sku: 'LAP-001',
      barcode: '1234567890123',
      basePrice: new Decimal(800.00),
      sellingPrice: new Decimal(999.99),
      category: 'Electronics',
      currentStock: 10,
      minimumStock: 2,
      reorderPoint: 5,
      branchId: mainBranch.id,
    },
  })

  const mouseProduct = await prisma.product.create({
    data: {
      name: 'Wireless Mouse',
      description: 'Ergonomic wireless mouse',
      sku: 'MOU-001',
      barcode: '1234567890124',
      basePrice: new Decimal(15.00),
      sellingPrice: new Decimal(29.99),
      category: 'Electronics',
      currentStock: 50,
      minimumStock: 10,
      reorderPoint: 20,
      branchId: mainBranch.id,
    },
  })

  const chairProduct = await prisma.product.create({
    data: {
      name: 'Office Chair',
      description: 'Comfortable office chair with lumbar support',
      sku: 'CHR-001',
      barcode: '1234567890125',
      basePrice: new Decimal(120.00),
      sellingPrice: new Decimal(199.99),
      category: 'Furniture',
      currentStock: 5,
      minimumStock: 1,
      reorderPoint: 3,
      branchId: secondBranch.id,
    },
  })

  console.log('✅ Created products')

  // Create sample invoice
  const invoice = await prisma.invoice.create({
    data: {
      invoiceNumber: 'INV-001',
      subtotal: new Decimal(1029.98),
      taxAmount: new Decimal(103.00),
      totalAmount: new Decimal(1132.98),
      paymentStatus: PaymentStatus.PAID,
      paymentMethod: PaymentMethod.CARD,
      cashierId: staff.id,
      branchId: mainBranch.id,
    },
  })

  // Create invoice items using the product IDs we already have
  await prisma.invoiceItem.createMany({
    data: [
      {
        quantity: 1,
        unitPrice: new Decimal(999.99),
        subtotal: new Decimal(999.99),
        invoiceId: invoice.id,
        productId: laptopProduct.id,
      },
      {
        quantity: 1,
        unitPrice: new Decimal(29.99),
        subtotal: new Decimal(29.99),
        invoiceId: invoice.id,
        productId: mouseProduct.id,
      },
    ],
  })

  console.log('✅ Created sample invoice with items')
  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })