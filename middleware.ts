import { NextRequest, NextResponse } from 'next/server'
import { getAuthenticatedUser } from '@/lib/auth'

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/products',
  '/categories',
  '/orders',
  '/branches',
  '/users',
  '/settings',
  '/profile',
  '/api/products',
  '/api/categories',
  '/api/orders',
  '/api/branches',
  '/api/users',
  '/api/invoices'
]

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout',
  '/api/auth/refresh'
]

// Define owner-only routes
const ownerOnlyRoutes = [
  '/users',
  '/branches',
  '/settings',
  '/api/users',
  '/api/branches'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/api/_next/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )

  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )

  // If it's a public route, allow access
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // If it's a protected route, check authentication
  if (isProtectedRoute) {
    try {
      const user = await getAuthenticatedUser(request)
      
      if (!user) {
        // Redirect to login for page routes
        if (!pathname.startsWith('/api/')) {
          const loginUrl = new URL('/login', request.url)
          loginUrl.searchParams.set('redirect', pathname)
          return NextResponse.redirect(loginUrl)
        }
        
        // Return 401 for API routes
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Check owner-only routes
      const isOwnerOnlyRoute = ownerOnlyRoutes.some(route => 
        pathname === route || pathname.startsWith(route + '/')
      )

      if (isOwnerOnlyRoute && user.role !== 'OWNER') {
        // Redirect to dashboard for page routes
        if (!pathname.startsWith('/api/')) {
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }
        
        // Return 403 for API routes
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }

      // Add user info to request headers for API routes
      if (pathname.startsWith('/api/')) {
        const requestHeaders = new Headers(request.headers)
        requestHeaders.set('x-user-id', user.id)
        requestHeaders.set('x-user-email', user.email)
        requestHeaders.set('x-user-role', user.role)
        
        return NextResponse.next({
          request: {
            headers: requestHeaders
          }
        })
      }

      return NextResponse.next()
    } catch (error) {
      console.error('Middleware authentication error:', error)
      
      // Redirect to login for page routes
      if (!pathname.startsWith('/api/')) {
        const loginUrl = new URL('/login', request.url)
        loginUrl.searchParams.set('redirect', pathname)
        return NextResponse.redirect(loginUrl)
      }
      
      // Return 401 for API routes
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }
  }

  // For all other routes, allow access
  return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}